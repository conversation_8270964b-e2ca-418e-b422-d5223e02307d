'use client';

import { Quicksand } from 'next/font/google';
import Link from 'next/link';

const quicksand = Quicksand({
  subsets: ['latin'],
  variable: '--font-quicksand',
  weight: ['300', '400', '500', '600', '700']
});

export default function ParentPage() {
  return (
    <main
      className={`${quicksand.variable} font-quicksand flex min-h-screen items-center justify-center bg-gray-50 p-6`}
    >
      <div className="w-full max-w-4xl">
        {/* Welcome Section */}
        <div className="mb-8 rounded-2xl bg-white p-8 text-center shadow-xl border border-gray-200">
          {/* Logo / Avatar */}
          <div className="mb-6 flex justify-center">
            <div className="flex h-20 w-20 items-center justify-center rounded-full bg-purple-100">
              <svg
                className="h-10 w-10 text-purple-600"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                />
              </svg>
            </div>
          </div>

          <h1 className="text-5xl font-bold text-gray-800">
            Welcome! Parent
          </h1>
          <p className="mt-4 text-gray-600">
            Help translate books to make them accessible for children in different languages.
          </p>
        </div>

        {/* Quick Links Section */}
        <div className="flex justify-center">
          {/* Books Translator - Only available tool for parents */}
          <Link href="/admin/translator" className="group max-w-md">
            <div className="rounded-2xl bg-white p-6 shadow-xl border border-gray-200 transition-all duration-300 hover:shadow-2xl hover:scale-105 hover:border-purple-300">
              <div className="flex items-center justify-between mb-4">
                <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-purple-100 group-hover:bg-purple-200 transition-colors">
                  <svg
                    className="h-6 w-6 text-purple-600"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M8 7V5a2 2 0 012-2h4a2 2 0 012 2v2"
                    />
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      d="M7 13l3 3 7-7"
                    />
                  </svg>
                </div>
                <svg
                  className="h-5 w-5 text-gray-400 group-hover:text-purple-600 transition-colors"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">
                Books Translator
              </h3>
              <p className="text-gray-600 text-sm">
                Translate children's books to multiple languages with advanced text extraction and editing capabilities. Help make stories accessible to kids worldwide.
              </p>
              <div className="mt-4 flex items-center text-sm text-purple-600 font-medium">
                <span>Start Translating</span>
                <svg
                  className="ml-2 h-4 w-4"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"
                  />
                </svg>
              </div>
            </div>
          </Link>
        </div>

        {/* Additional Info */}
        <div className="mt-8 text-center">
          <p className="text-gray-500 text-sm">
            As a parent volunteer, you can help translate children's books to make them accessible in different languages.
          </p>
          <p className="text-gray-400 text-xs mt-2">
            Your contributions help children around the world enjoy stories in their native language.
          </p>
        </div>

        {/* Back to Home */}
        <div className="mt-6 text-center">
          <Link 
            href="/" 
            className="inline-flex items-center text-sm text-gray-500 hover:text-purple-600 transition-colors"
          >
            <svg
              className="mr-2 h-4 w-4"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
              />
            </svg>
            Back to Home
          </Link>
        </div>
      </div>
    </main>
  );
}
