"use client";

//pdf-translator.tsx

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { Quicksand } from 'next/font/google';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { RefreshCw, AlertCircle, CheckCircle } from 'lucide-react';
import * as pdfjsLib from 'pdfjs-dist';
import PDFEditor from '@/components/admin/pdf-editor';
import { TextBlock } from '@/types/pdf';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

const languageOptions = [
  { value: 'en', label: 'English', flag: '🇺🇸' },
  { value: 'es', label: 'Spanish', flag: '🇪🇸' },
  { value: 'fr', label: 'French', flag: '🇫🇷' },
  { value: 'de', label: 'German', flag: '🇩🇪' },
  { value: 'ja', label: 'Japanese', flag: '🇯🇵' },
  { value: 'ar', label: 'Arabic', flag: '🇸🇦' },
  { value: 'ru', label: 'Russian', flag: '🇷🇺' },
];

// Daftar file PDF yang tersedia di folder public
const pdfFiles = [
  { value: 'Pengalaman Seru Murid Baru', label: 'Pengalaman Seru Murid Baru' },
  { value: 'Kejutan Rapor Sekolah', label: 'Kejutan Rapor Sekolah' },
  { value: 'Pertunjukan Sulap Niko', label: 'Pertunjukan Sulap Niko' },
];

const quicksand = Quicksand({
  subsets: ["latin"],
  variable: "--font-quicksand",
  weight: ["300", "400", "500", "600", "700"]
});

interface ExtractedFontInfo {
  name: string;
  family: string;
  weight: string;
  italic: boolean;
}

// Set PDF.js worker for Next.js
pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://unpkg.com/pdfjs-dist@3.11.174/build/pdf.worker.min.js';

// Function to extract font details from PDF.js commonObjs
async function extractFontDetailsFromPage(
  page: pdfjsLib.PDFPageProxy,
  fontName: string
): Promise<ExtractedFontInfo> {
  try {
    const opList = await page.getOperatorList();

    for (let i = 0; i < opList.fnArray.length; i++) {
      if (opList.fnArray[i] === pdfjsLib.OPS.setFont) {
        const [fontNameInPdf] = opList.argsArray[i] as [string, number];

        if (fontNameInPdf === fontName) {
          const fontObj = page.commonObjs.get(fontNameInPdf);

          if (fontObj) {
            // 1. BaseFont berisi nama PostScript sesungguhnya
            const baseFont = (fontObj.baseFontName || fontObj.name || fontNameInPdf) as string;

            // 2. Hilangkan prefix subset 6 karakter + "+"
            const cleanName = baseFont.replace(/^[A-Z0-9]{6}\+/, '');

            // 3. Ekstrak berat (angka 3-4 digit)
            const weightMatch = cleanName.match(/-?(\d{3,4})(?=[A-Za-z-]*$)/);
            const numericWeight = weightMatch ? parseInt(weightMatch[1], 10) : 400;

            // 4. Tentukan berat teks
            let fontWeight = 'normal';
            if (numericWeight >= 600) fontWeight = 'bold';
            else if (numericWeight >= 500) fontWeight = '600'; // medium semibold
            else fontWeight = 'normal';

            // 5. Cek italic
            const italic = /italic|oblique/i.test(cleanName);

            // 6. Ambil nama family (buang angka & kata kunci gaya)
            const family = cleanName
              .replace(/-\d{3,4}[A-Za-z-]*$/, '') // "-300Italic"
              .replace(/(Regular|Normal|Bold|Italic|Oblique)$/i, '');

            return {
              name: baseFont,
              family: family || 'Helvetica',
              weight: fontWeight,
              italic,
            };
          }
        }
      }
    }
  } catch (err) {
    console.warn(`Font extraction failed for ${fontName}:`, err);
  }

  // Fallback parser untuk nama mentah
  const cleanFallback = fontName.replace(/^[A-Z0-9]{6}\+/, '');
  const wMatch = cleanFallback.match(/-?(\d{3,4})(?=[A-Za-z-]*$)/);
  const numW = wMatch ? parseInt(wMatch[1], 10) : 400;
  const fallbackWeight = numW >= 600 ? 'bold' : 'normal';
  const fallbackItalic = /italic|oblique/i.test(cleanFallback);
  const fallbackFamily = cleanFallback
    .replace(/-\d{3,4}[A-Za-z-]*$/, '')
    .replace(/(Regular|Normal|Bold|Italic|Oblique)$/i, '') || 'Helvetica';

  return {
    name: fontName,
    family: fallbackFamily,
    weight: fallbackWeight,
    italic: fallbackItalic,
  };
}

// Function to calculate text rotation from transform matrix
function calculateRotation(transform: number[]): number {
  const [a, b] = transform;
  return Math.atan2(b, a) * (180 / Math.PI);
}

// Function to extract text with positions using PDF.js
async function extractTextWithPositions(pdfBytes: Uint8Array): Promise<TextBlock[]> {
  try {
    const loadingTask = pdfjsLib.getDocument({
      data: pdfBytes,
      cMapUrl: '/cmaps/',
      cMapPacked: true
    });
    const pdfDocument = await loadingTask.promise;

    const textBlocks: TextBlock[] = [];
    let blockId = 1;

    // Use Promise.all to wait for all font extractions to complete
    for (let pageNum = 1; pageNum <= pdfDocument.numPages; pageNum++) {
      const page = await pdfDocument.getPage(pageNum);
      const textContent = await page.getTextContent();

      // Create an array of promises for each text item
      const itemPromises = textContent.items.map(async (item: any) => {
        if (item.str?.trim().length > 0) {
          const transform = item.transform;
          const x = Math.round(transform[4]);
          const y = Math.round(page.getViewport({ scale: 1.0 }).height - transform[5]);

          const fontInfo = await extractFontDetailsFromPage(page, item.fontName);
          const rotation = calculateRotation(transform);

          return {
            id: blockId++,
            text: item.str.trim(),
            x,
            y,
            size: Math.round(item.height),
            font: fontInfo.name,
            fontFamily: fontInfo.family,
            fontWeight: fontInfo.weight,
            italic: fontInfo.italic,
            rotation: Math.round(rotation * 100) / 100,
            color: { r: 0, g: 0, b: 0 },
            translated: '',
            pageNumber: pageNum,
            width: Math.round(item.width)
          };
        }
        return null; // Return null for items that don't meet the condition
      });

      // Wait for all item promises on the current page to resolve
      const resolvedItems = await Promise.all(itemPromises);
      textBlocks.push(...resolvedItems.filter(item => item !== null)); // Filter out nulls
    }

    return textBlocks;

  } catch (error) {
    console.error('Error extracting text with PDF.js:', error);
    // Fallback data for demonstration/error handling
    return [
      {
        id: 1,
        text: 'Kejutan Rapor Sekolah',
        x: 50,
        y: 750,
        size: 24,
        font: 'Helvetica-Bold',
        fontFamily: 'Helvetica',
        fontWeight: 'bold',
        italic: false,
        rotation: 0,
        color: { r: 0, g: 0, b: 0 },
        translated: '',
        pageNumber: 1,
        width: 200
      },
      {
        id: 2,
        text: 'Ini adalah cerita tentang Budi yang mendapat kejutan di sekolah.',
        x: 50,
        y: 700,
        size: 12,
        font: 'Helvetica',
        fontFamily: 'Helvetica',
        fontWeight: 'normal',
        italic: false,
        rotation: 0,
        color: { r: 0, g: 0, b: 0 },
        translated: '',
        pageNumber: 1,
        width: 400
      }
    ];
  }
}

export default function PDFTranslatorAdmin() {
  const [textBlocks, setTextBlocks] = useState<TextBlock[]>([]);
  const [status, setStatus] = useState<'idle' | 'loading' | 'translating' | 'done' | 'error'>('idle');
  const [targetLang, setTargetLang] = useState('en');
  const [selectedPdf, setSelectedPdf] = useState('');
  const [editedPdfBytes, setEditedPdfBytes] = useState<Uint8Array | null>(null);
  const [originalPdfBytes, setOriginalPdfBytes] = useState<Uint8Array | null>(null);

  const processPdf = useCallback(async (pdfFileName: string) => {
    setStatus('loading');
    setTextBlocks([]);
    setEditedPdfBytes(null);
    setOriginalPdfBytes(null);
    try {
      /* 1. Extract */
      const res = await fetch(`/${pdfFileName}_isi.pdf`);
      const pdfBytes = await res.arrayBuffer();
      const blocks = await extractTextWithPositions(new Uint8Array(pdfBytes));
      setTextBlocks(blocks);
      console.log("Extracted", blocks);
      
      /* 2. Load PDF asli */
      const originalRes = await fetch(`/${pdfFileName}_isi.pdf`);
      const originalBytes = await originalRes.arrayBuffer();
      setOriginalPdfBytes(new Uint8Array(originalBytes));

      /* 3. Translate */
      setStatus('translating');
      const translationRes = await fetch('/api/translate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ blocks, targetLang }),
      });
      const { translated } = await translationRes.json();
      const updated = blocks.map(b => {
        const t = translated.find((t: any) => t.id === b.id);
        return { ...b, translated: t?.translated || '' };
      });
      console.log("Translation", updated);
      setTextBlocks(updated);

      /* 4. Generate blank PDF untuk editor */
      const blankRes = await fetch(`/${pdfFileName}_kosong.pdf`);
      const blankBytes = await blankRes.arrayBuffer();
      setEditedPdfBytes(new Uint8Array(blankBytes));

      setStatus('done');
    } catch (e) {
      console.error(e);
      setStatus('error');
    }
  }, [targetLang]);

  useEffect(() => {
    if (selectedPdf) {
      processPdf(selectedPdf);
    }
  }, [selectedPdf, targetLang, processPdf]);

  const getStatusIcon = () => {
    switch (status) {
      case 'done':
        return <CheckCircle className="w-5 h-5 text-green-400" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-400" />;
      default:
        return <RefreshCw className="w-5 h-5 text-blue-400 animate-spin" />;
    }
  };

  return (
    <div className={cn("h-screen flex flex-col bg-gray-50 font-quicksand antialiased", quicksand.variable)}>
      {/* Header - Fixed height */}
      <div className="flex-shrink-0 bg-white/90 backdrop-blur-sm border-b border-gray-200 shadow-sm">
        <div className="px-4 sm:px-6 py-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <h1 className="text-xl sm:text-2xl font-bold text-gray-900">PDF Translator 📄↔️🌐</h1>
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4">
              {/* PDF Selection Dropdown */}
              <Select value={selectedPdf} onValueChange={setSelectedPdf}>
                <SelectTrigger className="w-full sm:w-60">
                  <SelectValue placeholder="Select PDF" />
                </SelectTrigger>
                <SelectContent>
                  {pdfFiles.map((file) => (
                    <SelectItem key={file.value} value={file.value}>
                      {file.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Language Selection Dropdown */}
              <Select value={targetLang} onValueChange={setTargetLang}>
                <SelectTrigger className="w-full sm:w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {languageOptions.map((lang) => (
                    <SelectItem key={lang.value} value={lang.value}>
                      <span className="mr-2">{lang.flag}</span>
                      {lang.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Status Badge */}
              <Badge variant="outline" className="border-purple-300 text-purple-700 bg-purple-50 justify-center">
                {getStatusIcon()}
                <span className="ml-2 capitalize">{status}</span>
              </Badge>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area - Takes remaining height */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full px-4 sm:px-6 py-4 sm:py-6">
          {!selectedPdf ? (
            <div className="h-full flex items-center justify-center">
              <Card className="bg-white border-gray-200 shadow-sm w-full max-w-md">
                <CardContent className="p-8">
                  <div className="text-center">
                    <p className="text-gray-600">Kindly select book first on the dropdown at header above.</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          ) : editedPdfBytes && originalPdfBytes && status === 'done' ? (
            <div className="h-full">
              <PDFEditor
                textBlocks={textBlocks}
                blankPdfBytes={editedPdfBytes}
                onExport={(pdfBytes) => {
                  // Handle export if needed
                }}
                selectedPdfName={selectedPdf}
                targetLanguage={targetLang}
                originalPdfBytes={originalPdfBytes}
              />
            </div>
          ) : (
            <div className="h-full flex items-center justify-center">
              <Card className="bg-white border-gray-200 shadow-sm w-full max-w-md">
                <CardContent className="p-8">
                  <div className="text-center">
                    <RefreshCw className="w-12 h-12 text-gray-400 mx-auto mb-4 animate-spin" />
                    <p className="text-gray-600">Processing PDF…</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}